<script lang="ts">
import { defineComponent } from 'vue';

import { merge } from 'lodash-es';

// 定义 DataMessage 中 data 属性的接口
interface MessageData {
  text?: string;
  html?: string;
  type: 'InputText' | 'PasteHtml';
  // 允许其他属性
  [key: string]: any;
}

// 定义发送给 OnlyOffice 插件的消息结构
class DataMessage {
  data: MessageData;
  frameEditorId = 'docEditor';
  guid = 'asc.{17d36b72-56f1-4cf6-9ced-92dba22f7a09}';
  name = 'connect';
  type = 'onExternalPluginMessage';

  constructor(data: MessageData) {
    this.data = data;
  }
}

// 声明全局的 DocsAPI 对象
declare global {
  interface Window {
    DocsAPI: {
      DocEditor: new (elementId: string, config: OnlyOfficeConfig) => OnlyOfficeDocEditor;
    };
  }
}

// 定义 DocEditor 实例的类型
interface OnlyOfficeDocEditor {
  destroyEditor(): void;
}

// 定义 OnlyOffice 的配置类型
interface OnlyOfficeConfig {
  height?: string;
  events?: {
    [key: string]: any;
    onDocumentReady?: () => void;
  };
  editorConfig?: {
    [key: string]: any;
    plugins?: {
      pluginsData?: string[];
    };
  };
  docServiceApiUrl?: string;
  [key: string]: any;
}

// 定义 API 调用的返回类型
interface OnlyOfficeConfigResponse {
  data: {
    data?: OnlyOfficeConfig;
  };
}

// 定义插入方法参数的类型
interface ParamInfo {
  paramCode?: string;
  code?: string; // 用于表格
  tableList?: Array<{ paramCode: string; paramName: string }>;
}

let docEditor: OnlyOfficeDocEditor | undefined; // 初始化为 undefined

export default defineComponent({
  name: 'OnlyOffice',
  components: {},
  props: {
    getConfigApi: {
      type: Function,
      required: true,
    },
  },
  emits: ['init-finished', 'initFinished'],
  data() {
    return {};
  },
  methods: {
    /**
     * 初始化
     * @public
     * @param fileId 文件ID
     * @param type 类型
     * @param isForm 是否为表单
     */
    init(fileId: number | string, type: string, isForm: boolean): boolean | void {
      if (!fileId) {
        if (docEditor) {
          docEditor.destroyEditor();
        }
        return false;
      }
      this.getConfigApi(fileId, type, isForm).then((result: OnlyOfficeConfigResponse) => {
        const resData: OnlyOfficeConfig = result.data.data ?? {};
        const localConfig: OnlyOfficeConfig = {
          height: '100%',
          events: {
            onDocumentReady: () => {
              this.$emit('initFinished');
            },
          },
        };
        const mergeConfig: OnlyOfficeConfig = merge(resData, localConfig);

        // 确保 docServiceApiUrl 存在才尝试加载
        if (mergeConfig.docServiceApiUrl) {
          this.loadScript(mergeConfig.docServiceApiUrl)
            .then(() => {
              if (docEditor) {
                docEditor.destroyEditor();
              }
              // 确保 window.DocsAPI 和 window.DocsAPI.DocEditor 存在
              if (window.DocsAPI && window.DocsAPI.DocEditor) {
                docEditor = new window.DocsAPI.DocEditor('doc_editor', mergeConfig);
              } else {
                console.error('DocsAPI 在脚本加载后不可用。');
              }
            })
            .catch((error) => {
              console.error('加载 OnlyOffice 脚本失败:', error);
            });
        } else {
          console.error('配置中缺少 docServiceApiUrl。');
        }
      });
    },
    /**
     * 向编辑器中的插件发送信息
     * @param data 消息数据
     */
    sendInfo(data: MessageData): void {
      const dataMessage = new DataMessage(data);
      // 访问 window.frames[0][0] 可能需要更健壮的错误处理或类型检查
      if (window.frames[0] && (window.frames[0] as any)[0]) {
        (window.frames[0] as any)[0].postMessage(JSON.stringify(dataMessage), '*');
      } else {
        console.warn('无法找到目标帧来发送消息。');
      }
    },
    /**
     * 插入内容控件 - 文本
     * @public
     * @param paramInfo 参数信息
     * @returns {void}
     */
    insertStringContentControl(paramInfo: ParamInfo = {}): void {
      this.sendInfo({
        text: `{{${paramInfo.paramCode}}}`,
        type: 'InputText',
      });
    },
    /**
     * 插入内容控件 - 表格
     * @public
     * @param paramInfo 参数信息
     */
    insertTableContentControl(paramInfo: ParamInfo = {}): void {
      let HTML = '<table border="1" width="100%">';
      HTML += '<tr>';
      const paramColumns = paramInfo.tableList;
      if (paramColumns) {
        for (const [i, item] of paramColumns.entries()) {
          HTML += i === 0 ? `<td>{{${paramInfo.code}}}${item.paramName}</td>` : `<td>${item.paramName}</td>`;
        }
      }
      HTML += '</tr>';
      HTML += '<tr>';
      if (paramColumns) {
        for (const item of paramColumns) {
          HTML += `<td>[${item.paramCode}]</td>`;
        }
      }
      HTML += '</tr>';
      HTML += '</table>';
      this.sendInfo({
        html: HTML,
        type: 'PasteHtml',
      });
    },
    /**
     * @description 动态加载脚本
     * @param url 脚本URL
     * @returns {Promise<string>}
     */
    loadScript(url: string): Promise<string> {
      return new Promise((resolve, reject) => {
        const existingScript: HTMLScriptElement | null = document.querySelector(`script[src="${url}"]`);
        if (existingScript) {
          console.warn('脚本已加载过:', url);
          resolve('脚本已加载过');
          return;
        }

        const script: HTMLScriptElement = document.createElement('script');
        script.src = url;
        script.async = true;

        // 使用 addEventListener 代替 onerror
        script.addEventListener('load', () => {
          resolve('脚本已成功加载');
        });

        // 使用 addEventListener 代替 onerror
        script.addEventListener('error', (ev: Event) => {
          console.error('脚本加载失败:', url, ev);
          reject(ev);
        });

        document.head.append(script);
      });
    },
  },
});
</script>

<template>
  <div id="doc-editor-inner">
    <div id="doc_editor"></div>
  </div>
</template>

<style lang="scss">
#doc-editor-inner {
  height: 100%;
}
</style>
