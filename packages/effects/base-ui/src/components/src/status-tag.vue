<script setup lang="ts">
import type { DictInfo } from '@vben/stores';

import { computed } from 'vue';

import { useDictStore } from '@vben/stores';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Tag, Tooltip } from 'ant-design-vue';
import { isNil } from 'lodash-es';

const props = defineProps({
  value: { type: [String, Number, null], default: '' },
  code: { type: [String], default: '' },
  enableTag: { type: Boolean, default: true },
  tooltipProps: {
    type: Object,
    default() {
      return {};
    },
  },
});
const dictStore = useDictStore();
const dictInfo = computed(() => {
  if (props?.code) {
    return dictStore.dictItemInfo(props.value as string, props.code) ?? ({} as DictInfo);
  }
  return {} as DictInfo;
});

const dictIcon = computed(() => dictInfo.value?.dictIcon);

const color = computed(() => {
  return dictInfo.value?.dictColor || 'default';
});
const isTag = computed(() => props.enableTag && dictInfo.value?.isTag === 1);
</script>

<template>
  <Tooltip v-if="!isNil(props.value)" v-bind="props.tooltipProps">
    <Tag v-if="isTag" :color="color">
      <div class="flex items-center">
        <VbenIcon v-if="dictIcon" :icon="dictIcon" class="mr-2 w-4" />
        <span> {{ dictStore.formatter(value, code) }}</span>
      </div>
    </Tag>
    <div v-else class="flex items-center">
      <VbenIcon v-if="dictIcon" :icon="dictIcon" class="mr-2 w-4" />
      <span>{{ dictStore.formatter(value, code) }}</span>
    </div>
  </Tooltip>
</template>

<style scoped></style>
