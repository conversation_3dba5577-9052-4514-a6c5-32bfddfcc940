<script lang="ts" setup>
import type { PropType } from 'vue';

import { computed, useSlots } from 'vue';

import { useDesign } from '#/hooks/web/useDesign';

import BasicTitle from './BasicTitle.vue';

const props = defineProps({
  helpMessage: {
    type: [String, Array] as PropType<string | string[]>,
    default: '',
  },
  content: {
    type: String,
    default: '',
  },
  contentPosition: {
    type: String as PropType<'center' | 'left' | 'right'>,
    default: 'left',
  },
  bordered: {
    type: Boolean,
    default: true,
  },
});
const slots = useSlots();
const { prefixCls } = useDesign('basic-caption');
const getClass = computed(() => [prefixCls, { [`${prefixCls}-border`]: props.bordered }]);
const getContentPosition = computed(() => {
  if (props.contentPosition === 'left') return 'flex-start';
  if (props.contentPosition === 'right') return 'flex-end';
  return props.contentPosition;
});
</script>
<template>
  <div :class="getClass">
    <div :class="`${prefixCls}-content`" :style="{ 'justify-content': getContentPosition }">
      <slot name="content" v-if="slots.content"></slot>
      <BasicTitle :help-message="helpMessage" v-if="!slots.content && content" normal>
        {{ content }}
      </BasicTitle>
    </div>
    <div :class="`${prefixCls}__action`" v-if="slots.action">
      <slot name="action" v-if="slots.action"></slot>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import '#/style/index.less';
@prefix-cls: ~'@{namespace}-basic-caption';

.@{prefix-cls} {
  min-height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  word-break: break-all;
  padding-top: 10px;
  padding-bottom: 10px;
  &-border {
    border-bottom: 1px solid @border-color-base;
  }
  &-content {
    flex: 1;
    display: flex;
  }
  &__action {
    margin-left: 8px;
    flex-shrink: 0;
  }
}
</style>
