<script setup lang="ts">
import type { InspectionInfo } from '#/api';

import { inject, ref } from 'vue';

import { FULL_FORM_ITEM_PROP } from '@vben/constants';

import BusinessTableForm from '#/views/operation/inspection/manage/components/business-table-form.vue';
import CheckTableForm from '#/views/operation/inspection/manage/components/check-table-form.vue';
import EnergyConsumptionTableForm from '#/views/operation/inspection/manage/components/energy-consumption-table-form.vue';
import { inspectionFormKey } from '#/views/operation/inspection/manage/provideKey';

defineProps({ title: { type: String, default: '核心企业投后管理情况' } });
const inspectionForm = inject(inspectionFormKey, ref<Partial<InspectionInfo>>({}));
</script>

<template>
  <div>
    <CheckTableForm key-prefix="cg" :title="title" />
    <EnergyConsumptionTableForm />
    <a-form
      v-bind="{
        colon: false,
        ...FULL_FORM_ITEM_PROP,
      }"
    >
      <a-form-item label="情况描述">
        <a-textarea v-model:value="inspectionForm.wait4" :rows="4" />
      </a-form-item>
      <BusinessTableForm />
      <a-form-item label="财务分析">
        <a-textarea v-model:value="inspectionForm.wait5" :rows="4" />
      </a-form-item>
    </a-form>
  </div>
</template>

<style></style>
