import { defineComponent, h } from 'vue';

import { BaseUpload } from '@vben/base-ui';

import { getDownloadFileLinkApi, uploadFileApi } from '#/api/core/file';

// 直接创建 SimpleUpload 组件
export const SimpleUpload = defineComponent({
  name: 'SimpleUpload',
  inheritAttrs: false,
  setup(_, { attrs, slots }) {
    // 默认值在前，传入的属性在后，这样传入的属性会覆盖默认值
    const finalProps = {
      uploadApi: uploadFileApi,
      previewApi: getDownloadFileLinkApi,
      ...attrs, // attrs 包含了所有传入的属性，包括 v-model 绑定
    };

    return () => h(BaseUpload, finalProps, slots);
  },
});
