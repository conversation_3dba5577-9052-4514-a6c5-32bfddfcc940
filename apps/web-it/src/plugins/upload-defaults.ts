import type { App } from 'vue';

import { getDownloadFileLinkApi, uploadFileApi } from '#/api/core/file';

// 定义注入的 key
export const UPLOAD_API_KEY = Symbol('uploadApi');
export const PREVIEW_API_KEY = Symbol('previewApi');

// 默认的上传配置
export const defaultUploadConfig = {
  uploadApi: uploadFileApi,
  previewApi: getDownloadFileLinkApi,
};

// Vue 插件
export function setupUploadDefaults(app: App) {
  app.provide(UPLOAD_API_KEY, uploadFileApi);
  app.provide(PREVIEW_API_KEY, getDownloadFileLinkApi);
}

// 组合式函数
export function useUploadDefaults() {
  return {
    uploadApi: uploadFileApi,
    previewApi: getDownloadFileLinkApi,
  };
}
