import type { Component } from 'vue';

import { defineComponent, h } from 'vue';

import { BaseUpload } from '@vben/base-ui';

import { getDownloadFileLinkApi, uploadFileApi } from '#/api/core/file';

// 默认配置
const DEFAULT_UPLOAD_CONFIG = {
  uploadApi: uploadFileApi,
  previewApi: getDownloadFileLinkApi,
} as const;

// 高阶组件工厂，为任何组件添加上传默认值
export function withUploadDefaults<T extends Component>(WrappedComponent: T, defaults?: Record<string, any>) {
  const finalDefaults = defaults || DEFAULT_UPLOAD_CONFIG;

  return defineComponent({
    name: `WithUploadDefaults(${WrappedComponent.name || 'Component'})`,
    inheritAttrs: false,
    props: {
      uploadApi: { type: Function, required: false, default: () => uploadFileApi },
      previewApi: { type: Function, required: false, default: () => getDownloadFileLinkApi },
    },
    setup(props, { attrs, slots }) {
      // 合并默认值和传入的属性，传入的属性优先级更高
      const mergedProps = {
        ...finalDefaults,
        ...props,
        ...attrs,
      };

      return () => h(WrappedComponent, mergedProps, slots);
    },
  });
}

// 直接创建 SimpleUpload 组件，避免复杂的高阶组件问题
export const SimpleUpload = defineComponent({
  name: 'SimpleUpload',
  inheritAttrs: false,
  setup(_, { attrs, slots }) {
    // 默认值在前，传入的属性在后，这样传入的属性会覆盖默认值
    const finalProps = {
      uploadApi: uploadFileApi,
      previewApi: getDownloadFileLinkApi,
      ...attrs, // attrs 包含了所有传入的属性，包括 v-model 绑定
    };

    return () => h(BaseUpload, finalProps, slots);
  },
});
