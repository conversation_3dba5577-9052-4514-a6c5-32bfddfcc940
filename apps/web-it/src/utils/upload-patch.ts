import { BaseUpload } from '@vben/base-ui';

import { getDownloadFileLinkApi, uploadFileApi } from '#/api/core/file';

// 为 BaseUpload 组件添加默认值的补丁
export function patchBaseUploadDefaults() {
  // 保存原始的 props 定义
  const originalProps = BaseUpload.props;
  
  // 创建新的 props 定义，添加默认值
  const patchedProps = {
    ...originalProps,
    uploadApi: {
      ...originalProps.uploadApi,
      default: () => uploadFileApi,
      required: false,
    },
    previewApi: {
      ...originalProps.previewApi,
      default: () => getDownloadFileLinkApi,
      required: false,
    },
  };
  
  // 应用补丁
  BaseUpload.props = patchedProps;
  
  return BaseUpload;
}

// 创建一个已经打了补丁的 BaseUpload 组件
export const PatchedBaseUpload = patchBaseUploadDefaults();
