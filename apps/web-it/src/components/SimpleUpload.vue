<script setup lang="ts">
import type { UploadListType } from 'ant-design-vue/es/upload/interface';
import type { PropType } from 'vue';

import { BaseUpload } from '@vben/base-ui';

import { getDownloadFileLinkApi, uploadFileApi } from '#/api/core/file';

const props = defineProps({
  listType: { type: String as PropType<UploadListType>, default: 'picture-card' },
  maxCount: { type: Number, default: 1 },
  showUploadList: { type: Boolean, default: false },
  btnText: { type: String, default: '上传' },
  // 可选的 API 覆盖，如果不传则使用默认值
  uploadApi: { type: Function, default: () => uploadFileApi },
  previewApi: { type: Function, default: () => getDownloadFileLinkApi },
});

const emit = defineEmits(['uploadSuccess']);

// 定义 v-model
const fileId = defineModel({ type: Number });
const fileLink = defineModel('link', { type: String });
const fileInfo = defineModel('info', { type: Object });
const fileIdList = defineModel('idList', { type: Array });
</script>

<template>
  <BaseUpload
    v-model="fileId"
    v-model:link="fileLink"
    v-model:info="fileInfo"
    v-model:id-list="fileIdList"
    :list-type="listType"
    :max-count="maxCount"
    :show-upload-list="showUploadList"
    :btn-text="btnText"
    :upload-api="uploadApi"
    :preview-api="previewApi"
    v-bind="$attrs"
    @upload-success="emit('uploadSuccess', $event)"
  >
    <template v-for="(_, name) in $slots" #[name]="slotData">
      <slot :name="name" v-bind="slotData" />
    </template>
  </BaseUpload>
</template>

<style scoped></style>
