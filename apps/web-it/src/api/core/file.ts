import type { RequestClientConfig } from '@vben/request';

import { requestClient } from '#/api/request';

export async function uploadFileApi(data: any, config: RequestClientConfig) {
  return requestClient.upload('/infra/attach/upload', data, config);
}
export async function getPreviewFileLinkApi(params: { id: string }) {
  return requestClient.get('/infra/attach/preview-link', { params });
}
export async function getDownloadFileLinkApi(params: { id: string }) {
  return requestClient.get('/infra/attach/file-link', { params });
}
