import type { ConfigInfo } from '@vben/types';

import { requestClient } from '#/api/request';
import type {RequestClientConfig} from "@vben/request";

export interface CalendarConfigInfo {
  /**
   * 日历类型
   * STATUTORY: 法定工作日 CUSTOM: 自定义
   */
  type: 'CUSTOM' | 'STATUTORY';

  /**
   * 模板类型
   * STATUTORY: 法定工作日 STANDARD: 标准工作日
   */
  template?: 'STANDARD' | 'STATUTORY';

  /**
   * 年份，例如 2025
   */
  year?: number;

  /**
   * 基准周工作日，1-7 对应周一到周日
   */
  weekdays?: number[];

  /**
   * 实际有效工作日数组，格式为 "YYYY-MM-DD"
   */
  workingDays?: string[];
}

// 获取节假日设置
export async function getHolidaySettingsApi() {
  return requestClient.get('/upms/config/get_holiday_settings');
}
// 获取自定义节假日设置
export async function getCustomHolidaySettingsApi(params: { year: number }) {
  return requestClient.get('/upms/config/get_custom_holiday_settings', {
    params,
  });
}
// 获取法定工作日
export async function getLegalWorkdaysApi(params: { year: number }) {
  return requestClient.get('/upms/config/get_legal_workdays', { params });
}
// 保存节假日设置
export async function saveHolidaySettingsApi(data: CalendarConfigInfo) {
  return requestClient.post('/upms/config/save_holiday_settings', data);
}
// 获取配置信息
export async function getConfigInfoApi(): Promise<ConfigInfo> {
  return requestClient.get('/upms/config/list');
}
// 保存配置信息
export async function saveConfigInfoApi(data: Partial<ConfigInfo>) {
  return requestClient.post('/upms/config/save', data);
}
export async function getAdminListApi() {
  return requestClient.get('/upms/config/admin');
}
export async function saveAdminApi(data: string[]) {
  return requestClient.post('/upms/config/admin/save', data);
}
export async function saveOssConfigApi(data: any) {
  return requestClient.post('/infra/oss/config/save', data);
}
export async function getOssConfigApi() {
  return requestClient.get('/infra/oss/config/info');
}
export async function debugOssConfigApi(data: any, config: RequestClientConfig) {
  return requestClient.upload('/infra/oss/config/debug', data, config);
}
