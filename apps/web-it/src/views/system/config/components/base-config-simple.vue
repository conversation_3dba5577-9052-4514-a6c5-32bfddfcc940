<script setup lang="ts">
import type { Ref } from 'vue';

import type { ConfigInfo } from '@vben/types';

import { inject, ref } from 'vue';

import BasicHelp from '@vben/fe-ui/components/Basic/src/BasicHelp.vue';

import { Button, Col, Form, FormItem, Input, Row, Switch } from 'ant-design-vue';

// 使用应用内的 SimpleUpload 组件，不需要导入 API
import SimpleUpload from '#/components/SimpleUpload.vue';
import {
  configInfoInjectionKey,
  configLoadingInjectionKey,
  configSaveInjectionKey,
} from '#/views/system/config/config-injection-key';

const configForm = inject(configInfoInjectionKey) as Ref<ConfigInfo>;
const configLoading = inject(configLoadingInjectionKey) as Ref<boolean>;
const configSave = inject(configSaveInjectionKey) as () => Promise<void>;

const systemFormRef = ref();
const rules = {
  title: [{ required: true, message: '请输入系统名称', trigger: 'blur' }],
};
</script>

<template>
  <Row>
    <Col :span="12">
      <Form
        ref="systemFormRef"
        :model="configForm"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        :colon="false"
      >
        <!-- 系统LOGO - 使用 SimpleUpload，不需要传递 API -->
        <FormItem name="imgLogo">
          <template #label>
            系统LOGO
            <BasicHelp text="显示在登录页面和系统导航栏的网站图标" />
          </template>
          <SimpleUpload v-model:link="configForm.imgLogo" accept="image/*" />
        </FormItem>
        
        <!-- 系统图标 - 使用 SimpleUpload，不需要传递 API -->
        <FormItem name="imgFavicon">
          <template #label>
            系统图标
            <BasicHelp text="浏览器标签页显示的网站图标" />
          </template>
          <SimpleUpload v-model:link="configForm.imgFavicon" accept="image/*,.ico" />
        </FormItem>
        
        <!-- 登录背景 - 使用 SimpleUpload，不需要传递 API -->
        <FormItem name="imgLoginBackground">
          <template #label>
            登录背景
            <BasicHelp text="显示在登录页面的背景图片" />
          </template>
          <SimpleUpload v-model:link="configForm.imgLoginBackground" />
        </FormItem>
        
        <!-- 系统名称 -->
        <FormItem name="title">
          <template #label>
            系统名称
            <BasicHelp text="显示在浏览器标题栏、登录界面和导航栏的系统名称" />
          </template>
          <Input v-model:value="configForm.title" />
        </FormItem>
        
        <!-- 版权信息 -->
        <FormItem name="copyright">
          <template #label>
            版权信息
            <BasicHelp text="显示在页面底部的版权声明文本" />
          </template>
          <Input v-model:value="configForm.copyright" />
        </FormItem>
        
        <!-- 备案号 -->
        <FormItem name="icpLicense">
          <template #label>
            备案号
            <BasicHelp text="工信部 ICP 备案编号（如：京ICP备12345678号）" />
          </template>
          <Input v-model:value="configForm.icpLicense" />
        </FormItem>
        
        <!-- 导航栏显示 -->
        <FormItem name="isNavbarShow">
          <template #label>
            导航栏显示
            <BasicHelp text="是否在导航栏显示系统名称" />
          </template>
          <Switch v-model:checked="configForm.isNavbarShow" />
        </FormItem>
        
        <!-- 保存按钮 -->
        <FormItem :wrapper-col="{ offset: 6, span: 18 }">
          <Button type="primary" :loading="configLoading" @click="configSave">
            保存配置
          </Button>
        </FormItem>
      </Form>
    </Col>
  </Row>
</template>

<style scoped></style>
