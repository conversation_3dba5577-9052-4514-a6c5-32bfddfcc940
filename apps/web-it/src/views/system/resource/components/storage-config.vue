<script setup lang="ts">
import { BaseUpload } from '@vben/base-ui';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { Button, Col, message, Row } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { debugOssConfigApi, getOssConfigApi, saveOssConfigApi } from '#/api';

const dictStore = useDictStore();
const [StorageForm, storageFormApi] = useVbenForm(
  defineFormOptions({
    schema: [
      {
        component: 'Select',
        fieldName: 'platform',
        label: '存储平台',
        componentProps: {
          options: dictStore.getDictList('OSS_PLATFORM'),
        },
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'endpoint',
        label: '资源地址',
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'domain',
        label: '外网地址',
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'bucket',
        label: 'Bucket',
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'accessKey',
        label: 'AccessKey',
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'secretKey',
        label: 'SecretKey',
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'appId',
        label: 'AppId',
        rules: 'required',
        dependencies: {
          if(values) {
            return ['tencent'].includes(values.platform);
          },
          triggerFields: ['platform'],
        },
      },
      {
        component: 'Input',
        fieldName: 'region',
        label: 'Region',
        rules: 'required',
        dependencies: {
          if(values) {
            return ['huawei', 'tencent'].includes(values.platform);
          },
          triggerFields: ['platform'],
        },
      },
    ],
    wrapperClass: 'grid-cols-1',
    resetButtonOptions: {
      show: false,
    },
    submitButtonOptions: {
      content: '保存',
    },
    async handleSubmit(values) {
      await saveOssConfigApi(values);
      message.success('保存成功');
    },
  }),
);
const init = async () => {
  const res = await getOssConfigApi();
  await storageFormApi.setValues(res);
};
init();
const debugSuccess = () => {
  message.success('调试成功');
};
</script>

<template>
  <Row>
    <Col :span="12">
      <StorageForm>
        <template #reset-before>
          <BaseUpload :upload-api="debugOssConfigApi" list-type="text" @upload-success="debugSuccess">
            <Button>调试</Button>
          </BaseUpload>
        </template>
      </StorageForm>
    </Col>
  </Row>
</template>

<style scoped></style>
